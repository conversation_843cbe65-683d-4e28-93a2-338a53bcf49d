# AIVIA Backend Setup Guide

## Automated Setup (Recommended)

For a new server setup, simply run:

```bash
# Start the entire system
docker-compose up --build -d
```

This will automatically:
1. Start ZooKeeper with health checks
2. Start Kafka with health checks  
3. Initialize all required Kafka topics during web service startup
4. Start all consumer services after the web service is ready
5. Start the Kafka UI for monitoring

## Manual Setup

If you prefer to set up step by step:

### 1. Start the services
```bash
docker-compose up --build -d
```

### 2. The system will automatically:
- Start ZooKeeper and wait for it to be healthy
- Start Kafka and wait for it to be healthy  
- Start the web service which initializes Kafka topics as part of its startup
- Start all consumer services after the web service is running
- Start Kafka UI for monitoring

### 3. Verify the setup
```bash
# Check service status
docker-compose ps

# Verify Kafka topics were created
docker-compose exec kafka kafka-topics --bootstrap-server localhost:9092 --list

# View logs if needed
docker-compose logs -f [service-name]
```

## Service Dependencies

The Docker Compose setup includes proper service dependencies:

```
zookeeper (with health check)
    ↓
kafka (with health check)
    ↓
web (initializes topics during startup)
    ↓
stage-orchestrator, n8n-caller, data-processor
```

## Kafka Topics

The following topics are automatically created during web service startup:
- `video-creation-events` - Main video lifecycle events
- `stage-events` - Individual stage processing events  
- `n8n-integration-events` - N8N integration events
- `error-events` - Error handling events
- `retry-events` - Retry mechanism events
- `dlq-events` - Dead letter queue events

## Troubleshooting

### If Kafka initialization fails:
```bash
# Check web service logs for topic initialization
docker-compose logs web

# Manually run topic initialization
docker-compose exec web python manage.py init_kafka_topics
```

### If consumer services can't connect:
```bash
# Restart consumer services
docker-compose restart stage-orchestrator n8n-caller data-processor
```

### Clean restart:
```bash
# Stop all services and remove volumes
docker-compose down --volumes

# Start fresh
docker-compose up --build -d
```

## Access Points

- **Web Application**: http://localhost:8000
- **Kafka UI**: http://localhost:8080 (for monitoring Kafka topics and messages)

## Startup Sequence

1. **ZooKeeper** starts and becomes healthy
2. **Kafka** starts and becomes healthy  
3. **Web service** starts, initializes Kafka topics, then starts Django server
4. **Consumer services** start after web service is running
5. **Kafka UI** starts for monitoring
