"""
Stage orchestrator consumer - handles stage transitions and workflow logic
"""
import logging
from typing import Dict, Any, List
from django.db import transaction

from events.schemas import EventType

from .base import BaseEventConsumer
from .stage_initiation_handler import StageInitiationHandler
from ..kafka_config import KafkaConfig
from ..producer import event_publisher

logger = logging.getLogger(__name__)


class StageOrchestratorConsumer(BaseEventConsumer):
    """
    Handles stage completion events and orchestrates workflow transitions
    """
    
    def __init__(self):
        topics = [KafkaConfig.TOPICS['STAGE_EVENTS']]
        super().__init__(
            group_id='stage-orchestrator',
            topics=topics
        )
    
    def handle_event(self, event_data: Dict[str, Any], headers: Dict[str, str]) -> bool:
        """Handle stage-related events"""
        try:
            event_type = headers.get('event_type')
            
            if event_type == EventType.STAGE_COMPLETED.value:
                return self._handle_stage_completed(event_data)
            elif event_type == EventType.STAGE_FAILED.value:
                return self._handle_stage_failed(event_data)
            elif event_type == EventType.STAGE_INITIATED.value:
                return self._handle_stage_initiated(event_data)
            else:
                logger.debug(f"Ignoring event type: {event_type}")
                return True
                
        except Exception as e:
            logger.error(f"Error handling stage event: {e}")
            return False
    
    def _handle_stage_completed(self, event_data: Dict[str, Any]) -> bool:
        """Handle stage completion and determine next stage"""
        try:
            video_id = event_data['video_id']
            stage = event_data['stage']
            correlation_id = event_data['correlation_id']
            
            logger.info(f"Processing stage completion: {stage} for video {video_id}")
            
            # Get video and update workflow state
            with transaction.atomic():
                from videos.models import Video
                from ..models import WorkflowState
                
                video = Video.objects.select_for_update().get(id=video_id)
                workflow_state, created = WorkflowState.objects.get_or_create(
                    video_id=video,
                    defaults={
                        'correlation_id': correlation_id,
                        'current_stage': stage
                    }
                )
                
                # Mark stage as completed
                workflow_state.mark_stage_completed(stage)
                
                # Determine next stage
                next_stage = self._get_next_stage(stage)
                
                if next_stage:
                    # Check if auto approval is enabled
                    if video.auto_approval_each_stage:
                        # Auto approval enabled - proceed automatically to next stage
                        logger.info(f"Auto approval enabled - proceeding automatically to next stage: {next_stage}")
                        
                        # Update workflow state
                        workflow_state.current_stage = next_stage
                        workflow_state.save(update_fields=['current_stage', 'updated_at'])
                        
                        # Update video stage
                        video.stage = next_stage
                        video.save(update_fields=['stage'])
                        
                        logger.info(f"Moving video {video_id} from stage '{stage}' to '{next_stage}'")
                        
                        # Use the stage initiation handler
                        success = StageInitiationHandler.handle_stage_initiation(
                            video_id=video_id,
                            stage=next_stage,
                            correlation_id=correlation_id
                        )
                        
                        if not success:
                            logger.error(f"Failed to initiate next stage {next_stage}")
                            return False
                    else:
                        # Auto approval disabled - wait for manual approval
                        logger.info(f"Auto approval disabled - waiting for manual approval before proceeding to {next_stage}")
                        
                        # Update video status to indicate stage completion awaiting approval
                        video.status = 'waiting_for_review'
                        video.save(update_fields=['status'])
                        
                        # Publish stage awaiting approval event
                        try:
                            from ..producer import event_publisher
                            event_publisher.publish_stage_awaiting_approval(
                                video_id=video_id,
                                completed_stage=stage,
                                next_stage=next_stage,
                                correlation_id=correlation_id,
                                message=f"Stage '{stage}' completed successfully. Manual approval required to proceed to '{next_stage}'"
                            )
                            logger.info(f"Published stage awaiting approval event for video {video_id}")
                        except Exception as e:
                            logger.error(f"Failed to publish stage awaiting approval event: {e}")
                        
                        logger.info(f"Video {video_id} stage '{stage}' completed, awaiting manual approval for '{next_stage}'")
                        
                else:
                    # All stages completed
                    workflow_state.current_stage = 'completed'
                    workflow_state.save(update_fields=['current_stage', 'updated_at'])
                    
                    video.stage = 'completed'
                    video.status = 'done'
                    video.save(update_fields=['stage', 'status'])
                    
                    logger.info(f"Video {video_id} creation completed successfully")
                    
                    # Publish video completion event
                    from ..schemas import EventFactory
                    completion_event = EventFactory.create_video_creation_started(
                        video_id=video_id,
                        user_id=video.user_id,
                        correlation_id=correlation_id
                    )
                    completion_event.event_type = 'VideoCreationCompleted'
                    
                    from ..producer import EventProducer
                    producer = EventProducer()
                    producer.publish_event(completion_event)
            
            return True
            
        except Exception as e:
            logger.error(f"Error handling stage completion: {e}")
            return False
    
    def _handle_stage_failed(self, event_data: Dict[str, Any]) -> bool:
        """Handle stage failure and determine retry strategy"""
        try:
            video_id = event_data['video_id']
            stage = event_data['stage']
            correlation_id = event_data['correlation_id']
            error_message = event_data.get('error_message', 'Unknown error')
                        
            with transaction.atomic():
                from videos.models import Video
                from ..models import WorkflowState, RetryPolicy
                
                video = Video.objects.select_for_update().get(id=video_id)
                workflow_state, created = WorkflowState.objects.get_or_create(
                    video_id=video,
                    defaults={
                        'correlation_id': correlation_id,
                        'current_stage': stage
                    }
                )
                
                # Get current retry count BEFORE marking as failed (to get accurate count)
                current_retry_count = workflow_state.get_retry_count(stage)
                logger.warning(f"Processing stage failure: {stage} for video {video_id}, retry {current_retry_count}")

                # Get retry policy
                retry_policy = RetryPolicy.objects.filter(
                    event_type=EventType.STAGE_FAILED.value,
                    stage=stage,
                    is_active=True
                ).first()
                
                if not retry_policy:
                    retry_policy = RetryPolicy.objects.filter(
                        event_type=EventType.STAGE_FAILED.value,
                        stage__isnull=True,
                        is_active=True
                    ).first()
                
                max_retries = retry_policy.max_retries if retry_policy else 3
                
                # Check if we should retry BEFORE incrementing the count
                if current_retry_count < max_retries:
                    # Mark stage as failed (this increments retry count from 0 to 1, or 1 to 2, etc.)
                    workflow_state.mark_stage_failed(stage)
                    # Get the new retry count after increment
                    new_retry_count = workflow_state.get_retry_count(stage)
                    # Calculate delay with exponential backoff (use new_retry_count - 1 for 0-based indexing in delay calculation)
                    delay_seconds = retry_policy.calculate_delay(new_retry_count - 1) if retry_policy else (60 * (2 ** (new_retry_count - 1)))
                    delay_seconds = min(delay_seconds, 3600)  # Max 1 hour delay
                    
                    logger.warning(f"Scheduling retry {new_retry_count}/{max_retries} for stage {stage} of video {video_id} "
                               f"after {delay_seconds} seconds")
                    
                    # Implement delayed retry mechanism using threading
                    import time
                    from threading import Timer
                    
                    def delayed_retry():
                        """Execute the retry after delay"""
                        try:
                            success = event_publisher.publish_stage_initiated(
                                video_id=video_id,
                                stage=stage,
                                correlation_id=correlation_id,
                                retry_count=new_retry_count  # Pass new retry count
                            )
                            if success:
                                logger.info(f"Successfully published retry {new_retry_count} for stage {stage}, video {video_id}")
                            else:
                                logger.error(f"Failed to publish retry event for stage {stage}, video {video_id}")
                        except Exception as e:
                            logger.error(f"Error in delayed retry for stage {stage}: {e}")
                    
                    # Schedule the retry with delay
                    timer = Timer(delay_seconds, delayed_retry)
                    timer.daemon = True  # Dies when main thread dies
                    timer.start()
                    
                    # Update video status
                    video.status = 'in_progress'
                    video.error = f"Retry {new_retry_count}/{max_retries} scheduled: {error_message}"
                    video.save(update_fields=['status', 'error'])
                        
                else:
                    # Max retries exceeded - mark video as failed (don't increment retry count further)
                    logger.error(f"Max retries ({max_retries}) exceeded for stage {stage}, video {video_id}")
                    
                    video.status = 'error'
                    video.error = f"Stage {stage} failed after {max_retries} retries: {error_message}"
                    video.save(update_fields=['status', 'error'])
            return True
            
        except Exception as e:
            logger.error(f"Error handling stage failure: {e}")
            return False
    
    def _handle_stage_initiated(self, event_data: Dict[str, Any]) -> bool:
        """Handle stage initiation - update tracking and initiate actual stage processing"""
        try:
            video_id = event_data['video_id']
            stage = event_data['stage']
            correlation_id = event_data['correlation_id']
            
            logger.info(f"Processing stage initiation: {stage} for video {video_id}")
            
            # Log the event
            from ..models import VideoEventLog
            VideoEventLog.objects.create(
                video_id=video_id,
                event_type=EventType.STAGE_INITIATED.value,
                stage=stage,
                correlation_id=correlation_id,
                event_data=event_data,
                status='processed'
            )
            
            # Actually initiate the stage processing
            success = StageInitiationHandler.handle_stage_initiation(
                video_id=video_id,
                stage=stage,
                correlation_id=correlation_id
            )
            
            if not success:
                logger.error(f"Failed to initiate stage processing for {stage} on video {video_id}")
                return False
            
            logger.info(f"Successfully initiated stage processing: {stage} for video {video_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error handling stage initiation: {e}")
            return False
    
    def _get_next_stage(self, current_stage: str) -> str:
        """Get the next stage in the video creation flow"""
        from videos.constants import VIDEO_CREATION_FLOW
        
        try:
            current_index = VIDEO_CREATION_FLOW.index(current_stage)
            if current_index < len(VIDEO_CREATION_FLOW) - 1:
                return VIDEO_CREATION_FLOW[current_index + 1]
            return None
        except ValueError:
            logger.error(f"Unknown stage: {current_stage}")
            return None
