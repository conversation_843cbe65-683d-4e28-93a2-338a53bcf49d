"""
Event schema definitions for video creation workflow
"""
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum


class EventType(Enum):
    # Video Lifecycle Events
    VIDEO_CREATION_STARTED = "VideoCreationStarted"
    VIDEO_CREATION_COMPLETED = "VideoCreationCompleted" 
    VIDEO_CREATION_FAILED = "VideoCreationFailed"
    
    # Stage Events
    STAGE_INITIATED = "StageInitiated"
    STAGE_COMPLETED = "StageCompleted"
    STAGE_FAILED = "StageFailed"
    STAGE_SKIPPED = "StageSkipped"
    STAGE_AWAITING_APPROVAL = "StageAwaitingApproval"
    
    # N8N Integration Events
    N8N_STAGE_REQUESTED = "N8NStageRequested"
    N8N_CALLBACK_RECEIVED = "N8NCallbackReceived"
    N8N_CALL_FAILED = "N8NCallFailed"
    
    # Control Events
    RETRY_STAGE_REQUESTED = "RetryStageRequested"
    WORKFLOW_PAUSED = "WorkflowPaused"
    WORKFLOW_RESUMED = "WorkflowResumed"


class EventStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"


@dataclass
class BaseEvent:
    """Base event structure with common fields"""
    event_id: str
    event_type: str
    timestamp: datetime
    video_id: int
    correlation_id: str
    version: str = "1.0"
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        # Convert datetime to ISO format
        data['timestamp'] = self.timestamp.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        # Convert ISO format back to datetime
        if isinstance(data['timestamp'], str):
            data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)


@dataclass
class VideoLifecycleEvent(BaseEvent):
    """Events related to overall video lifecycle"""
    user_id: Optional[int] = None
    task_id: Optional[int] = None
    account_id: Optional[int] = None
    video_type: Optional[str] = None
    status: Optional[str] = None
    error_message: Optional[str] = None


@dataclass
class StageEvent(BaseEvent):
    """Events related to individual stage processing"""
    stage: Optional[str] = None
    execution_id: Optional[str] = None
    stage_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    error_code: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3


@dataclass
class N8NIntegrationEvent(BaseEvent):
    """Events related to N8N integration"""
    stage: Optional[str] = None
    n8n_url: Optional[str] = None
    webhook_url: Optional[str] = None
    payload: Optional[Dict[str, Any]] = None
    response_data: Optional[Dict[str, Any]] = None
    execution_id: Optional[str] = None
    error_message: Optional[str] = None
    response_status_code: Optional[int] = None


@dataclass
class ControlEvent(BaseEvent):
    """Events related to workflow control"""
    stage: Optional[str] = None
    reason: Optional[str] = None
    requested_by: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class EventFactory:
    """Factory class to create events"""
    
    @staticmethod
    def create_video_creation_started(
        video_id: int,
        user_id: int,
        correlation_id: str,
        task_id: Optional[int] = None,
        account_id: Optional[int] = None,
        video_type: Optional[str] = None
    ) -> VideoLifecycleEvent:
        return VideoLifecycleEvent(
            event_id=str(uuid.uuid4()),
            event_type=EventType.VIDEO_CREATION_STARTED.value,
            timestamp=datetime.utcnow(),
            video_id=video_id,
            correlation_id=correlation_id,
            user_id=user_id,
            task_id=task_id,
            account_id=account_id,
            video_type=video_type,
            status="started"
        )
    
    @staticmethod
    def create_stage_initiated(
        video_id: int,
        stage: str,
        correlation_id: str,
        execution_id: Optional[str] = None,
        retry_count: int = 0
    ) -> StageEvent:
        return StageEvent(
            event_id=str(uuid.uuid4()),
            event_type=EventType.STAGE_INITIATED.value,
            timestamp=datetime.utcnow(),
            video_id=video_id,
            correlation_id=correlation_id,
            stage=stage,
            execution_id=execution_id,
            retry_count=retry_count
        )
    
    @staticmethod
    def create_stage_completed(
        video_id: int,
        stage: str,
        correlation_id: str,
        stage_data: Optional[Dict[str, Any]] = None,
        execution_id: Optional[str] = None
    ) -> StageEvent:
        return StageEvent(
            event_id=str(uuid.uuid4()),
            event_type=EventType.STAGE_COMPLETED.value,
            timestamp=datetime.utcnow(),
            video_id=video_id,
            correlation_id=correlation_id,
            stage=stage,
            stage_data=stage_data,
            execution_id=execution_id
        )
    
    @staticmethod
    def create_stage_awaiting_approval(
        video_id: int,
        completed_stage: str,
        next_stage: str,
        correlation_id: str,
        message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> ControlEvent:
        return ControlEvent(
            event_id=str(uuid.uuid4()),
            event_type=EventType.STAGE_AWAITING_APPROVAL.value,
            timestamp=datetime.utcnow(),
            video_id=video_id,
            correlation_id=correlation_id,
            stage=completed_stage,
            reason=message or f"Stage '{completed_stage}' completed. Manual approval required to proceed to '{next_stage}'",
            requested_by='system',
            metadata={
                'completed_stage': completed_stage,
                'next_stage': next_stage,
                'requires_manual_approval': True,
                **(metadata or {})
            }
        )
    
    @staticmethod
    def create_n8n_callback_received(
        video_id: int,
        stage: str,
        correlation_id: str,
        response_data: Dict[str, Any],
        execution_id: Optional[str] = None
    ) -> N8NIntegrationEvent:
        return N8NIntegrationEvent(
            event_id=str(uuid.uuid4()),
            event_type=EventType.N8N_CALLBACK_RECEIVED.value,
            timestamp=datetime.utcnow(),
            video_id=video_id,
            correlation_id=correlation_id,
            stage=stage,
            response_data=response_data,
            execution_id=execution_id
        )
    
    @staticmethod
    def create_n8n_stage_requested(
        video_id: int,
        stage: str,
        correlation_id: str,
        n8n_url: str,
        webhook_url: str,
        payload: Dict[str, Any]
    ) -> N8NIntegrationEvent:
        return N8NIntegrationEvent(
            event_id=str(uuid.uuid4()),
            event_type=EventType.N8N_STAGE_REQUESTED.value,
            timestamp=datetime.utcnow(),
            video_id=video_id,
            correlation_id=correlation_id,
            stage=stage,
            n8n_url=n8n_url,
            webhook_url=webhook_url,
            payload=payload
        )
    
    @staticmethod
    def create_stage_failed(
        video_id: int,
        stage: str,
        correlation_id: str,
        error_message: str,
        error_code: Optional[str] = None,
        retry_count: int = 0
    ) -> StageEvent:
        return StageEvent(
            event_id=str(uuid.uuid4()),
            event_type=EventType.STAGE_FAILED.value,
            timestamp=datetime.utcnow(),
            video_id=video_id,
            correlation_id=correlation_id,
            stage=stage,
            error_message=error_message,
            error_code=error_code,
            retry_count=retry_count
        )
    
    @staticmethod
    def create_retry_stage_requested(
        video_id: int,
        stage: str,
        correlation_id: str,
        requested_by: str,
        reason: Optional[str] = None,
        retry_count: int = 0,
        metadata: Optional[Dict[str, Any]] = None
    ) -> ControlEvent:
        return ControlEvent(
            event_id=str(uuid.uuid4()),
            event_type=EventType.RETRY_STAGE_REQUESTED.value,
            timestamp=datetime.utcnow(),
            video_id=video_id,
            correlation_id=correlation_id,
            stage=stage,
            requested_by=requested_by,
            reason=reason,
            metadata={
                'retry_count': retry_count,
                **(metadata or {})
            }
        )
    
    @staticmethod
    def create_stage_approved(
        video_id: int,
        stage: str,
        next_stage: str,
        correlation_id: str,
        approved_by: str,
        feedback: Optional[str] = None,
        reason: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> ControlEvent:
        return ControlEvent(
            event_id=str(uuid.uuid4()),
            event_type='StageApproved',
            timestamp=datetime.utcnow(),
            video_id=video_id,
            correlation_id=correlation_id,
            stage=stage,
            requested_by=approved_by,
            reason=reason,
            metadata={
                'next_stage': next_stage,
                'feedback': feedback,
                'action': 'approved',
                **(metadata or {})
            }
        )
    
    @staticmethod
    def create_stage_rejected(
        video_id: int,
        stage: str,
        correlation_id: str,
        rejected_by: str,
        feedback: Optional[str] = None,
        reason: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> ControlEvent:
        return ControlEvent(
            event_id=str(uuid.uuid4()),
            event_type='StageRejected',
            timestamp=datetime.utcnow(),
            video_id=video_id,
            correlation_id=correlation_id,
            stage=stage,
            requested_by=rejected_by,
            reason=reason,
            metadata={
                'feedback': feedback,
                'action': 'rejected',
                **(metadata or {})
            }
        )
    
    @staticmethod
    def create_video_completed(
        video_id: int,
        correlation_id: str,
        completion_data: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ) -> ControlEvent:
        return ControlEvent(
            event_id=str(uuid.uuid4()),
            event_type='VideoCompleted',
            timestamp=datetime.utcnow(),
            video_id=video_id,
            correlation_id=correlation_id,
            stage='completed',
            requested_by='system',
            reason='Video creation process completed',
            metadata={
                'completion_data': completion_data,
                **(metadata or {})
            }
        )
