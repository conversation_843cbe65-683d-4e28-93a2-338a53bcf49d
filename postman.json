{"info": {"name": "AIVIA VideoAgent API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": "Comprehensive Postman collection for AIVIA Video Automation Backend with Kafka integration, User Configuration management, and Video Retry functionality. Updated with latest features including event-driven architecture and stage-based retry system.", "version": "3.0.0"}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "description": "Base URL for the API"}, {"key": "token", "value": "", "description": "Authentication token - set after login"}, {"key": "active_video_id", "value": "1", "description": "Active video ID for testing - update with actual video ID"}, {"key": "test_video_id", "value": "", "description": "Test video ID - automatically set during test creation"}, {"key": "last_retry_correlation_id", "value": "", "description": "Last retry correlation ID - automatically set after retry operations"}, {"key": "draft_task_id", "value": "", "description": "Draft task ID - automatically set when saving drafts"}, {"key": "production_task_id", "value": "", "description": "Production task ID - automatically set when proceeding with tasks"}], "item": [{"name": "Authentication", "item": [{"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/auth/register", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"name\": \"Test User\",\n  \"password\": \"Pass@1234\",\n  \"confirm_password\": \"Pass@1234\",\n  \"mobile\": \"1234567890\"\n}"}}}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/auth/verify-otp", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"otp\": \"123456\"\n}"}}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/auth/login", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Pass@1234\"\n}"}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "pm.environment.set('token', json.token);"]}}]}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}, {"key": "Authorization", "value": "Token {{token}}"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/profile"}}, {"name": "Change Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/change-password", "body": {"mode": "raw", "raw": "{\n  \"old_password\": \"Pass@1234\",\n  \"new_password\": \"NewPass@1234\",\n  \"confirm_password\": \"NewPass@1234\"\n}"}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/logout"}}]}, {"name": "User Configuration", "description": "User configuration management with JSON storage", "item": [{"name": "Get User Configuration", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/config", "description": "Retrieve current user configuration. Returns 404 if no configuration exists."}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "console.log('User Config:', JSON.stringify(json, null, 2));"]}}]}, {"name": "Save User Configuration (Create/Update)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/config", "body": {"mode": "raw", "raw": "{\n  \"config\": {\n    \"theme\": \"dark\",\n    \"language\": \"en\",\n    \"video_preferences\": {\n      \"default_orientation\": \"landscape\",\n      \"default_duration\": 60,\n      \"auto_publish\": false,\n      \"quality_settings\": {\n        \"resolution\": \"1080p\",\n        \"bitrate\": \"high\",\n        \"encoding\": \"h264\"\n      },\n      \"retry_settings\": {\n        \"max_retries\": 3,\n        \"retry_delay\": 60,\n        \"escalation_enabled\": true\n      }\n    },\n    \"notifications\": {\n      \"email\": true,\n      \"push\": false,\n      \"sms\": false,\n      \"retry_alerts\": true,\n      \"completion_alerts\": true\n    },\n    \"integrations\": {\n      \"social_media\": {\n        \"auto_post\": false,\n        \"platforms\": [\"youtube\", \"tiktok\"]\n      },\n      \"storage\": {\n        \"cloud_backup\": true,\n        \"local_copy\": false\n      }\n    },\n    \"kafka_integration\": {\n      \"enabled\": true,\n      \"retry_events\": true,\n      \"event_tracking\": true\n    }\n  }\n}"}, "description": "Save or update user configuration. Merges with existing configuration if it exists."}}, {"name": "Update Specific Settings (Partial Update)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/config", "body": {"mode": "raw", "raw": "{\n  \"config\": {\n    \"theme\": \"light\",\n    \"notifications\": {\n      \"email\": false\n    },\n    \"last_updated\": \"2024-08-21T10:00:00Z\"\n  }\n}"}, "description": "Partial update - only updates specified fields, keeps existing settings intact"}}, {"name": "Replace Configuration (Complete Replace)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/config", "body": {"mode": "raw", "raw": "{\n  \"config\": {\n    \"version\": \"2.0\",\n    \"minimal_setup\": true,\n    \"kafka_integration\": {\n      \"enabled\": true,\n      \"retry_events\": true\n    },\n    \"features\": [\"user_config\", \"video_retry\", \"kafka_events\"]\n  }\n}"}, "description": "Completely replaces existing configuration with new data"}}, {"name": "Delete User Configuration", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/config", "description": "Delete user configuration completely"}}, {"name": "Advanced Configuration Example", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/config", "body": {"mode": "raw", "raw": "{\n  \"config\": {\n    \"user_profile\": {\n      \"display_name\": \"Content Creator Pro\",\n      \"timezone\": \"UTC-8\",\n      \"preferred_language\": \"en-US\"\n    },\n    \"workspace_settings\": {\n      \"auto_save_interval\": 30,\n      \"backup_frequency\": \"daily\",\n      \"collaboration_enabled\": true\n    },\n    \"video_creation_defaults\": {\n      \"script_type\": \"from_user_idea\",\n      \"tts_provider\": \"openai\",\n      \"tts_voice\": \"alloy\",\n      \"video_style\": \"modern\",\n      \"background_music\": \"upbeat_corporate\",\n      \"image_provider\": \"dalle\",\n      \"orientation\": \"landscape\",\n      \"duration\": 60,\n      \"quality\": \"high\"\n    },\n    \"automation_rules\": {\n      \"auto_retry_failed_stages\": true,\n      \"max_auto_retries\": 2,\n      \"retry_delay_minutes\": 5,\n      \"notify_on_completion\": true,\n      \"notify_on_failure\": true\n    },\n    \"api_preferences\": {\n      \"response_format\": \"detailed\",\n      \"include_metadata\": true,\n      \"timeout_seconds\": 30\n    },\n    \"dashboard_layout\": {\n      \"widgets\": [\"recent_videos\", \"queue_status\", \"performance_metrics\"],\n      \"refresh_interval\": 60,\n      \"show_advanced_options\": true\n    }\n  }\n}"}, "description": "Example of advanced configuration with comprehensive settings"}}]}, {"name": "Accounts", "item": [{"name": "List Accounts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts"}}, {"name": "Create Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts", "body": {"mode": "raw", "raw": "{\n  \"name\": \"MyC<PERSON><PERSON>\",\n  \"topic\": \"Tech Reviews\",\n  \"platforms\": [\"YouTube\",\"TikTok\"],\n  \"credentials\": {\"api_key\":\"abc123\"},\n  \"language\": \"english\",\n  \"status\": \"active\"\n}"}}}, {"name": "Get Account", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts/1"}}, {"name": "Update Account", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts/1", "body": {"mode": "raw", "raw": "{\n  \"name\": \"MyUpdatedChannel\"\n}"}}}, {"name": "Delete Account", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts/1"}}]}, {"name": "Video Tasks", "item": [{"name": "List Video Tasks", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks"}}, {"name": "Save Draft Task", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/save_draft", "body": {"mode": "raw", "raw": "{\n  \"video_type\": \"faceless\",\n  \"context\": \"Create an educational video about renewable energy sources like solar and wind power. Explain their benefits and environmental impact.\",\n  \"script_type\": \"from_user_idea\",\n  \"speech_type\": \"tts\",\n  \"tts_provider\": \"openai\",\n  \"tts_voice\": \"alloy\",\n  \"video_style\": \"modern\",\n  \"bgm\": \"upbeat_corporate\",\n  \"image_provider\": \"dalle\",\n  \"video_provider\": \"default\",\n  \"orientation\": \"landscape\",\n  \"duration\": 60\n}"}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "if (json.task_id) {", "    pm.environment.set('draft_task_id', json.task_id);", "}"]}}]}, {"name": "Update Draft Task", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/save_draft", "body": {"mode": "raw", "raw": "{\n  \"id\": {{draft_task_id}},\n  \"video_type\": \"avatar\",\n  \"context\": \"Updated: Create a comprehensive guide about artificial intelligence and machine learning technologies\",\n  \"script_type\": \"from_user_idea\",\n  \"speech_type\": \"tts\",\n  \"tts_provider\": \"elevenlabs\",\n  \"tts_voice\": \"rachel\",\n  \"video_style\": \"cinematic\",\n  \"bgm\": \"calm_piano\",\n  \"image_provider\": \"dalle\",\n  \"video_provider\": \"default\",\n  \"orientation\": \"portrait\",\n  \"duration\": 90\n}"}}}, {"name": "Save and Proceed Task (Start Video Creation)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/save_and_proceed", "body": {"mode": "raw", "raw": "{\n  \"video_type\": \"faceless\",\n  \"context\": \"Create an engaging video about the future of electric vehicles and sustainable transportation. Cover battery technology, charging infrastructure, and environmental benefits.\",\n  \"script_type\": \"from_user_idea\",\n  \"speech_type\": \"tts\",\n  \"tts_provider\": \"openai\",\n  \"tts_voice\": \"nova\",\n  \"video_style\": \"dynamic\",\n  \"bgm\": \"electronic_beat\",\n  \"image_provider\": \"dalle\",\n  \"video_provider\": \"default\",\n  \"orientation\": \"landscape\",\n  \"duration\": 120,\n  \"auto_approval_each_stage\": true\n}"}, "description": "Create task with automatic stage progression (default behavior)"}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "if (json.task_id) {", "    pm.environment.set('production_task_id', json.task_id);", "}", "if (json.video_id) {", "    pm.environment.set('active_video_id', json.video_id);", "}"]}}]}, {"name": "Save and Proceed Task (Manual Approval Flow)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/save_and_proceed", "body": {"mode": "raw", "raw": "{\n  \"video_type\": \"faceless\",\n  \"context\": \"Create a high-quality video about artificial intelligence and machine learning in healthcare. Focus on diagnostic applications, treatment personalization, and ethical considerations.\",\n  \"script_type\": \"from_user_idea\",\n  \"speech_type\": \"tts\",\n  \"tts_provider\": \"elevenlabs\",\n  \"tts_voice\": \"rachel\",\n  \"video_style\": \"professional\",\n  \"bgm\": \"calm_corporate\",\n  \"image_provider\": \"dalle\",\n  \"video_provider\": \"default\",\n  \"orientation\": \"landscape\",\n  \"duration\": 180,\n  \"auto_approval_each_stage\": false\n}"}, "description": "Create task requiring manual approval at each stage"}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "if (json.task_id) {", "    pm.environment.set('manual_task_id', json.task_id);", "}", "if (json.videos && json.videos.length > 0) {", "    pm.environment.set('manual_video_id', json.videos[0].video_id);", "}"]}}]}, {"name": "Update and Proceed Task", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/save_and_proceed", "body": {"mode": "raw", "raw": "{\n  \"id\": {{draft_task_id}},\n  \"video_type\": \"avatar\",\n  \"context\": \"Updated and ready for production: Digital transformation in modern businesses\",\n  \"script_type\": \"from_user_idea\",\n  \"speech_type\": \"tts\",\n  \"tts_provider\": \"elevenlabs\",\n  \"tts_voice\": \"drew\",\n  \"video_style\": \"minimal\",\n  \"bgm\": \"acoustic_folk\",\n  \"image_provider\": \"dalle\",\n  \"video_provider\": \"default\",\n  \"orientation\": \"square\",\n  \"duration\": 60\n}"}}}, {"name": "Get Video Task", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/1"}}, {"name": "Delete Video Task", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/1"}}]}, {"name": "Videos", "item": [{"name": "List Videos", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos"}}, {"name": "Get Video Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}"}}, {"name": "Get Video Status (New)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/status", "description": "Get detailed video creation progress including stage, progress percentage, and estimated completion time"}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "console.log('Video Status:', JSON.stringify(json, null, 2));"]}}]}, {"name": "Update Video", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}", "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Video Title\",\n  \"description\": \"Updated video description with more details\"\n}"}}}]}, {"name": "Video Retry System (Kafka-Enabled)", "description": "Stage-based video retry functionality with Kafka event integration", "item": [{"name": "Retry Script Generation Stage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/script_generation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Script quality needs improvement - retry with enhanced prompts\"\n}"}, "description": "Retry the script generation stage. This is usually the first stage and has no dependencies."}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "if (json.correlation_id) {", "    pm.environment.set('last_retry_correlation_id', json.correlation_id);", "}", "console.log('Retry Response:', JSON.stringify(json, null, 2));"]}}]}, {"name": "Retry Image Prompt Generation Stage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/image_prompt_generation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Image prompts need to be more specific and detailed\"\n}"}, "description": "Retry image prompt generation. Depends on script generation being completed."}}, {"name": "Retry Clip Creation Stage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/clip_creation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Clip timing and transitions need adjustment\"\n}"}, "description": "Retry clip creation stage. Depends on image prompt generation being completed."}}, {"name": "Retry Track Creation Stage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/track_creation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Audio track configuration needs refinement\"\n}"}, "description": "Retry track creation stage. Depends on clip creation being completed."}}, {"name": "Retry Voice Generation Stage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/voice_generation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Voice quality or pronunciation needs improvement\"\n}"}, "description": "Retry voice generation stage. Depends on script generation being completed."}}, {"name": "Retry Final Composition Stage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/final_composition", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Final video composition needs quality improvements\"\n}"}, "description": "Retry final composition stage. Depends on all previous stages being completed."}}, {"name": "Retry with Metadata", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/script_generation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Comprehensive retry with enhanced settings\",\n  \"metadata\": {\n    \"retry_strategy\": \"immediate\",\n    \"priority\": \"high\",\n    \"enhanced_prompts\": true,\n    \"quality_boost\": true,\n    \"user_feedback\": \"Previous script was too technical, make it more accessible\"\n  }\n}"}, "description": "Example of retry with additional metadata for enhanced processing"}}, {"name": "Test Invalid Stage (Error Case)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/invalid_stage", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Testing error handling for invalid stage\"\n}"}, "description": "Test error handling for invalid stage names"}}, {"name": "Test Non-existent Video (Error Case)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/99999/retry/script_generation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Testing error handling for non-existent video\"\n}"}, "description": "Test error handling for non-existent video IDs"}}]}, {"name": "Manual Video Flow Control", "description": "Manual progression control for videos with auto_approval_each_stage disabled", "item": [{"name": "Proceed to Next Stage (Approve)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/proceed_to_next_stage", "body": {"mode": "raw", "raw": "{\n  \"approve\": true,\n  \"feedback\": \"Stage looks good, approved for progression\",\n  \"reason\": \"Manual approval after review\"\n}"}, "description": "Approve current stage and proceed to next stage for videos with manual flow control"}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "if (json.correlation_id) {", "    pm.environment.set('last_progression_correlation_id', json.correlation_id);", "}", "console.log('Manual Progression Response:', JSON.stringify(json, null, 2));"]}}]}, {"name": "Reject Current Stage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/proceed_to_next_stage", "body": {"mode": "raw", "raw": "{\n  \"approve\": false,\n  \"feedback\": \"Quality needs improvement. Script requires more engaging content and better flow.\",\n  \"reason\": \"Content quality review - needs revision\"\n}"}, "description": "Reject current stage and require rework"}}, {"name": "Test Auto-Approval Video (Error Case)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/proceed_to_next_stage", "body": {"mode": "raw", "raw": "{\n  \"approve\": true,\n  \"reason\": \"Testing error case for auto-approval enabled video\"\n}"}, "description": "Test error handling when trying manual progression on auto-approval video"}}, {"name": "Approve with Detailed Fe<PERSON>back", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{test_video_id}}/proceed_to_next_stage", "body": {"mode": "raw", "raw": "{\n  \"approve\": true,\n  \"feedback\": \"Excellent work on this stage. The script is engaging, well-structured, and perfectly matches our brand voice. Voice generation sounds natural and professional. Ready to proceed to next stage.\",\n  \"reason\": \"Quality approval after comprehensive review\"\n}"}, "description": "Approve stage with comprehensive feedback"}}]}, {"name": "Kafka Event Monitoring", "description": "Endpoints and examples for monitoring Kafka events and retry operations", "item": [{"name": "Get Video Workflow States", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/workflow-states", "description": "Get all workflow states for a video to track retry attempts and stage progression"}}, {"name": "Search by Correlation ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/workflow-states?correlation_id={{last_retry_correlation_id}}", "description": "Search workflow states by correlation ID to track specific retry operations"}}, {"name": "Get Retry Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry-stats", "description": "Get retry statistics and metrics for a specific video"}}, {"name": "List Recent Events", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/events/recent?event_type=RetryStageRequested&limit=10", "description": "List recent retry events (if event API is available)"}}, {"name": "Get Event by Correlation ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/events/{{last_retry_correlation_id}}", "description": "Get specific event details by correlation ID"}}]}, {"name": "Testing and Validation", "description": "Comprehensive testing scenarios for the new features", "item": [{"name": "Test Complete Workflow", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/save_and_proceed", "body": {"mode": "raw", "raw": "{\n  \"video_type\": \"faceless\",\n  \"context\": \"TEST: Create a test video about Kafka integration and retry functionality. This video will be used to test the new retry system with event-driven architecture.\",\n  \"script_type\": \"from_user_idea\",\n  \"speech_type\": \"tts\",\n  \"tts_provider\": \"openai\",\n  \"tts_voice\": \"alloy\",\n  \"video_style\": \"modern\",\n  \"bgm\": \"upbeat_corporate\",\n  \"image_provider\": \"dalle\",\n  \"video_provider\": \"default\",\n  \"orientation\": \"landscape\",\n  \"duration\": 30\n}"}, "description": "Create a test video to validate the complete retry workflow"}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "if (json.video_id) {", "    pm.environment.set('test_video_id', json.video_id);", "    console.log('Created test video with ID:', json.video_id);", "}"]}}]}, {"name": "Configuration + Retry Test Sequence", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/config", "body": {"mode": "raw", "raw": "{\n  \"config\": {\n    \"test_session\": {\n      \"timestamp\": \"{{$timestamp}}\",\n      \"test_id\": \"integration_test_{{$randomInt}}\",\n      \"kafka_enabled\": true\n    },\n    \"retry_preferences\": {\n      \"auto_retry\": false,\n      \"notification_level\": \"all\",\n      \"max_attempts\": 3\n    },\n    \"features_under_test\": [\n      \"user_config_crud\",\n      \"video_retry_kafka\",\n      \"stage_validation\",\n      \"event_correlation\"\n    ]\n  }\n}"}, "description": "Save test configuration before running retry tests"}}, {"name": "Validate Stage Dependencies", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{test_video_id}}/retry/final_composition", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Testing dependency validation - this should fail if previous stages are not completed\"\n}"}, "description": "Test stage dependency validation by trying to retry final stage first"}}, {"name": "Test Multiple Retries", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{test_video_id}}/retry/script_generation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Multiple retry test - attempt #{{$randomInt}}\",\n  \"metadata\": {\n    \"test_type\": \"multiple_retry\",\n    \"attempt_number\": \"{{$randomInt}}\",\n    \"batch_id\": \"test_{{$timestamp}}\"\n  }\n}"}, "description": "Test multiple retry attempts to validate correlation tracking"}}, {"name": "Cleanup Test Configuration", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/config", "description": "Clean up test configuration after testing"}}]}, {"name": "Video Creation Flow - N8N Callbacks", "description": "Endpoints for N8N to send callbacks during video creation stages", "item": [{"name": "Generic Stage Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/script", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"stage\": \"script\",\n  \"execution_id\": \"exec_12345\",\n  \"script\": \"Welcome to our comprehensive guide on renewable energy. In this video, we'll explore the revolutionary impact of solar and wind power technologies...\",\n  \"title\": \"Renewable Energy: Powering the Future\",\n  \"description\": \"An in-depth look at how renewable energy sources are transforming our world and creating a sustainable future for generations to come.\"\n}", "description": "Callback for script generation stage completion"}}}, {"name": "Script Generation Stage Success Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/script", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_script_creation_001\",\n  \"script\": \"Renewable energy is revolutionizing how we power our world. Solar panels convert sunlight into electricity with incredible efficiency, while wind turbines harness natural air currents to generate clean power. These technologies are not just environmentally friendly – they're also becoming more cost-effective than traditional fossil fuels, making them the smart choice for our energy future.\",\n  \"title\": \"The Renewable Energy Revolution\",\n  \"description\": \"Discover how solar and wind power are transforming our energy landscape and creating a sustainable future.\"\n}"}}}, {"name": "Voice Generation Stage Success Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/voice", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_voice_generation_001\",\n  \"speech_url\": \"https://storage.example.com/audio/voice_{{active_video_id}}.mp3\",\n  \"avatar_url\": \"https://storage.example.com/avatars/avatar_{{active_video_id}}.mp4\"\n}"}}}, {"name": "Caption Generation Stage Success Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/caption", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_caption_generation_001\",\n  \"caption_data\": {\n    \"subtitle_url\": \"https://storage.example.com/subtitles/video_{{active_video_id}}.srt\",\n    \"timing_data\": [\n      {\"start\": 0, \"end\": 3, \"text\": \"Renewable energy is revolutionizing\"},\n      {\"start\": 3, \"end\": 6, \"text\": \"how we power our world.\"}\n    ]\n  }\n}"}}}, {"name": "Image Prompt Generation Stage Success Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/image_prompt", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_image_prompt_generation_001\",\n  \"image_prompts\": [\n    {\n      \"prompt\": \"Modern solar panel installation on residential rooftop with blue sky background, cinematic lighting, high resolution\",\n      \"scene_timing\": {\"start\": 0, \"duration\": 5}\n    },\n    {\n      \"prompt\": \"Wind turbines in a field during golden hour, rotating blades, sustainable energy concept, professional photography\",\n      \"scene_timing\": {\"start\": 5, \"duration\": 5}\n    },\n    {\n      \"prompt\": \"Electric vehicle charging station with green energy symbols, modern design, eco-friendly technology\",\n      \"scene_timing\": {\"start\": 10, \"duration\": 5}\n    }\n  ]\n}"}}}, {"name": "Image Generation Stage Success Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/image_generation", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_image_generation_001\",\n  \"generated_images\": [\n    {\n      \"image_url\": \"https://storage.example.com/images/solar_panels_001.jpg\",\n      \"prompt_id\": 1,\n      \"metadata\": {\n        \"resolution\": \"1920x1080\",\n        \"format\": \"jpg\",\n        \"size\": \"2.3MB\"\n      }\n    },\n    {\n      \"image_url\": \"https://storage.example.com/images/wind_turbines_001.jpg\",\n      \"prompt_id\": 2,\n      \"metadata\": {\n        \"resolution\": \"1920x1080\",\n        \"format\": \"jpg\",\n        \"size\": \"1.8MB\"\n      }\n    },\n    {\n      \"image_url\": \"https://storage.example.com/images/ev_charging_001.jpg\",\n      \"prompt_id\": 3,\n      \"metadata\": {\n        \"resolution\": \"1920x1080\",\n        \"format\": \"jpg\",\n        \"size\": \"2.1MB\"\n      }\n    }\n  ]\n}"}}}, {"name": "Clip Creation Stage Success Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/clip", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_clip_creation_001\",\n  \"clips\": [\n    {\n      \"track_type\": \"video\",\n      \"layer\": 0,\n      \"media_asset_id\": 1,\n      \"start_time\": 0.0,\n      \"in_point\": 0.0,\n      \"out_point\": 5.0,\n      \"opacity\": 1.0\n    },\n    {\n      \"track_type\": \"video\",\n      \"layer\": 0,\n      \"media_asset_id\": 2,\n      \"start_time\": 5.0,\n      \"in_point\": 0.0,\n      \"out_point\": 5.0,\n      \"opacity\": 1.0\n    },\n    {\n      \"track_type\": \"video\",\n      \"layer\": 0,\n      \"media_asset_id\": 3,\n      \"start_time\": 10.0,\n      \"in_point\": 0.0,\n      \"out_point\": 5.0,\n      \"opacity\": 1.0\n    }\n  ]\n}"}}}, {"name": "Track Creation Stage Success Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/tracks", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_track_creation_001\",\n  \"tracks\": [\n    {\n      \"type\": \"video\",\n      \"layer\": 0\n    },\n    {\n      \"type\": \"audio\",\n      \"layer\": 0\n    },\n    {\n      \"type\": \"audio\",\n      \"layer\": 1\n    }\n  ]\n}"}}}, {"name": "Video Composition Stage Success Callback (Final)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/compose", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_video_composition_001\",\n  \"production_url\": \"https://cdn.example.com/videos/final_{{active_video_id}}.mp4\",\n  \"raw_url\": \"https://storage.example.com/raw/video_{{active_video_id}}_raw.mp4\",\n  \"duration\": 120,\n  \"thumbnail_url\": \"https://cdn.example.com/thumbnails/video_{{active_video_id}}.jpg\",\n  \"metadata\": {\n    \"resolution\": \"1920x1080\",\n    \"fps\": 30,\n    \"bitrate\": \"5000kbps\",\n    \"format\": \"mp4\",\n    \"file_size\": \"45.2MB\"\n  }\n}"}}}, {"name": "Error Callback Example", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/voice", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"error\",\n  \"execution_id\": \"exec_voice_generation_001\",\n  \"error_message\": \"TTS service temporarily unavailable. Please try again later.\"\n}"}}}]}, {"name": "Media Management", "item": [{"name": "List Tracks", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tracks"}}, {"name": "List Media Generations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/media-generations"}}, {"name": "List Media Assets", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/media-assets"}}, {"name": "List Clips", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/clips"}}]}, {"name": "Legacy Webhooks", "item": [{"name": "n8n Video Callback (Legacy)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/webhook/n8n-callback/1", "body": {"mode": "raw", "raw": "{\n  \"video_url\": \"https://cdn.example.com/video.mp4\",\n  \"title\": \"Auto Generated Video\",\n  \"duration\": 120\n}"}}}]}, {"name": "Configurations", "item": [{"name": "Get Configuration Options", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/configurations", "description": "Get all available options for video creation including TTS voices, video styles, orientations, etc."}}]}, {"name": "Payments", "item": [{"name": "List Packages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/packages"}}, {"name": "Create Checkout Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/create-checkout-session", "body": {"mode": "raw", "raw": "{\n  \"package_id\": 1,\n  \"success_url\": \"http://localhost:3000/success\",\n  \"cancel_url\": \"http://localhost:3000/cancel\"\n}"}}}, {"name": "List User Packages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/user-packages"}}, {"name": "List Payment History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/payment-history"}}]}]}