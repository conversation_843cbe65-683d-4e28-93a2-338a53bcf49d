services:
  web:
    build: .
    volumes:
      - .:/app
    ports:
      - "8000:8000"
    depends_on:
      kafka:
        condition: service_healthy
    restart: unless-stopped

  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_4LW_COMMANDS_WHITELIST: "srvr,stat,ruok,mntr"
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    healthcheck:
      test: [ "CMD-SHELL", "nc -z localhost 2181 || exit 1" ]
      interval: 10s
      timeout: 5s
      retries: 5

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    depends_on:
      zookeeper:
        condition: service_healthy
    ports:
      - "9092:9092"
      - "29092:29092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'false' # We'll create topics explicitly
    volumes:
      - kafka_data:/var/lib/kafka/data
    healthcheck:
      test: [ "CMD-SHELL", "kafka-topics --bootstrap-server localhost:9092 --list > /dev/null 2>&1" ]
      interval: 10s
      timeout: 10s
      retries: 5

  # Event consumers - now depend on web service which initializes topics
  stage-orchestrator:
    build: .
    command: python manage.py run_stage_orchestrator
    depends_on:
      web:
        condition: service_started
    volumes:
      - .:/app
    restart: unless-stopped

  n8n-caller:
    build: .
    command: python manage.py run_n8n_caller
    depends_on:
      web:
        condition: service_started
    volumes:
      - .:/app
    restart: unless-stopped

  data-processor:
    build: .
    command: python manage.py run_data_processor
    depends_on:
      web:
        condition: service_started
    volumes:
      - .:/app
    restart: unless-stopped

  # Kafka UI for monitoring (optional)
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    depends_on:
      kafka:
        condition: service_healthy
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:9092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181

volumes:
  postgres_data:
  kafka_data:
  zookeeper_data:
  zookeeper_logs:
