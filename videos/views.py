from rest_framework import viewsets, permissions, status
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from django.conf import settings
import requests
from django.shortcuts import get_object_or_404

from .models import (
    VideoTask, Video, Track, 
    MediaGeneration, MediaAsset, Clip,
    TTSVoice, BGM
)
from .serializers import (
    VideoTaskSerializer, VideoListSerializer, VideoDetailSerializer,
    VideoCreateUpdateSerializer, TrackSerializer, MediaGenerationSerializer,
    MediaAssetSerializer, ClipSerializer
)
from .constants import (
    TASK_STATUS_CHOICES, VIDEO_PUBLISH_STATUS_CHOICES, VIDEO_STAGE_CHOICES, VIDEO_STATUS_CHOICES, VIDEO_TYPE_CHOICES, SCRIPT_TYPE_CHOICES, SPEECH_TYPE_CHOICES,
    VIDEO_STYLE_CHOICES, ORIENTATION_CHOICES, DURATION_CHOICES
)
from .utils.n8n_helpers import handle_video_creation_callback
from .services.video_creation_service import video_creation_service
import logging

logger = logging.getLogger(__name__)

class VideoTaskViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing video tasks.
    """
    serializer_class = VideoTaskSerializer
    
    def get_queryset(self):
        """
        Return video tasks for the current authenticated user.
        """
        return VideoTask.objects.filter(user=self.request.user).order_by('-created_at')

    @action(detail=False, methods=['post'])
    def save_draft(self, request):
        """
        API to create task in draft status or update existing draft task
        """
        task_id = request.data.get('id')
        logger.info(f"save_draft called with task_id: {task_id}")  # Better logging

        if task_id:
            # Update existing task
            try:
                task = VideoTask.objects.get(id=task_id, user=request.user)
                
                # Check if task is in draft status
                if task.status != 'draft':
                    return Response(
                        {'error': f'Cannot save task. Task is in "{task.status}" status and can only be modified when in "draft" status.'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                serializer = VideoTaskSerializer(task, data=request.data, partial=True, context={'request': request})
            except VideoTask.DoesNotExist:
                return Response(
                    {'error': 'Task not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
        else:
            # Create new task
            serializer = VideoTaskSerializer(data=request.data, context={'request': request})
        
        if serializer.is_valid():
            # Ensure status is always 'draft' for this endpoint
            task = serializer.save(user=request.user, status='draft')
            
            return Response({
                'status': 'success',
                'message': 'Task saved as draft successfully',
                'task_id': task.id,
                'task_status': task.status
            }, status=status.HTTP_200_OK if task_id else status.HTTP_201_CREATED)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['post'])
    def save_and_proceed(self, request):
        """
        API to create task in todo status and create video, then start the video creation flow
        """
        task_id = request.data.get('id')
        
        if task_id:
            # Update existing task
            try:
                task = VideoTask.objects.get(id=task_id, user=request.user)
                
                # Check if task is in draft status
                if task.status != 'draft':
                    return Response(
                        {'error': f'Cannot save and proceed with task. Task is in "{task.status}" status and can only be modified when in "draft" status.'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                serializer = VideoTaskSerializer(task, data=request.data, partial=True, context={'request': request})
            except VideoTask.DoesNotExist:
                return Response(
                    {'error': 'Task not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
        else:
            # Create new task
            serializer = VideoTaskSerializer(data=request.data, context={'request': request})
        
        if serializer.is_valid():
            # Set status to 'todo' and save
            task = serializer.save(user=request.user, status='todo')
            
            # Create Video instances from the task (one for each associated account)
            videos = self.create_videos_from_task(task)
            
            if videos:
                # Start the video creation flow for all videos
                successful_videos = []
                failed_videos = []
                
                for video in videos:
                    # Use event-driven video creation service
                    creation_success = video_creation_service.start_video_creation(video)
                    
                    if creation_success:
                        successful_videos.append(video)
                    else:
                        failed_videos.append(video)
                
                if successful_videos:
                    # Update task status to in progress
                    task.status = 'inprogress'
                    task.save()
                    
                    response_data = {
                        'status': 'success',
                        'message': f'Task saved and video creation started for {len(successful_videos)} account(s)',
                        'task_id': task.id,
                        'videos': [
                            {
                                'video_id': video.id,
                                'account_id': video.account.id if video.account else None,
                                'account_name': video.account.name if video.account else None,
                                'video_stage': video.stage
                            } for video in successful_videos
                        ],
                        'task_status': task.status,
                        'total_videos': len(videos),
                        'successful_videos': len(successful_videos),
                        'failed_videos': len(failed_videos)
                    }
                    
                    if failed_videos:
                        response_data['warning'] = f'{len(failed_videos)} video(s) failed to start creation'
                    
                    return Response(response_data, status=status.HTTP_200_OK if task_id else status.HTTP_201_CREATED)
                else:
                    # If all video creations fail, revert task status
                    task.status = 'draft'
                    task.save()
                    
                    return Response(
                        {'error': 'Task saved but all video creations failed. Task status reverted to draft.'},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )
            else:
                return Response(
                    {'error': 'Failed to create videos from task - no accounts associated with task'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def create_videos_from_task(self, task):
        """
        Create Video instances from a VideoTask for each associated account
        If no accounts are associated, create a single video without an account
        
        Args:
            task: VideoTask instance
            
        Returns:
            List of Video instances or empty list if creation fails
        """
        videos = []
        
        # Get all accounts associated with this task
        task_accounts = task.task_accounts.all()
        
        if not task_accounts.exists():
            # No accounts associated - create a single video without account
            logger.info(f"No accounts associated with task {task.id}, creating video without account")
            try:
                video = Video.objects.create(
                    user=task.user,
                    task=task,
                    account=None,  # No account associated
                    video_type=task.video_type,
                    speech_type=task.speech_type,
                    script_type=task.script_type,
                    tts_provider=task.tts_provider,
                    tts_voice=task.tts_voice,
                    video_style=task.video_style,
                    bgm=task.bgm,
                    context=task.context,
                    image_provider=task.image_provider,
                    video_provider=task.video_provider,
                    orientation=task.orientation,
                    duration=task.duration,
                    auto_approval_each_stage=task.auto_approval_each_stage,  # Task-level setting takes priority
                    status='in_queue',
                    stage='yet_to_start',
                )
                
                videos.append(video)
                logger.info(f"Created video {video.id} from task {task.id} without account, auto_approval: {task.auto_approval_each_stage}")
            except Exception as e:
                logger.error(f"Error creating video from task {task.id} without account: {str(e)}")
        else:
            # Create videos for each associated account
            for task_account in task_accounts:
                try:
                    video = Video.objects.create(
                        user=task.user,
                        task=task,
                        account=task_account.account,
                        video_type=task.video_type,
                        speech_type=task.speech_type,
                        script_type=task.script_type,
                        tts_provider=task.tts_provider,
                        tts_voice=task.tts_voice,
                        video_style=task.video_style,
                        bgm=task.bgm,
                        context=task.context,
                        image_provider=task.image_provider,
                        video_provider=task.video_provider,
                        orientation=task.orientation,
                        duration=task.duration,
                        auto_approval_each_stage=task.auto_approval_each_stage,  # Task-level setting takes priority
                        status='in_queue',
                        stage='yet_to_start',
                    )
                    
                    videos.append(video)
                    logger.info(f"Created video {video.id} from task {task.id} for account {task_account.account.id}, auto_approval: {task.auto_approval_each_stage}")
                except Exception as e:
                    logger.error(f"Error creating video from task {task.id} for account {task_account.account.id}: {str(e)}")
                    # Continue creating videos for other accounts even if one fails
                    continue
        
        return videos



class VideoViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing videos.
    """
    def get_serializer_class(self):
        if self.action == 'list':
            return VideoListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return VideoCreateUpdateSerializer
        return VideoDetailSerializer
    
    def get_queryset(self):
        """
        Return videos for the current authenticated user.
        """
        return Video.objects.filter(user=self.request.user).order_by('-created_at')
    
    def perform_create(self, serializer):
        """
        Associate the video with the current user when creating a new video.
        """
        serializer.save(user=self.request.user)
    
    @action(detail=True, methods=['get'])
    def status(self, request, pk=None):
        """
        Get detailed status of a video creation process
        """
        video = self.get_object()
        
        from .constants import VIDEO_CREATION_FLOW
        
        # Calculate progress
        progress_percentage = 0
        current_stage_index = -1
        
        if video.stage in VIDEO_CREATION_FLOW:
            current_stage_index = VIDEO_CREATION_FLOW.index(video.stage)
            total_stages = len(VIDEO_CREATION_FLOW)
            progress_percentage = (current_stage_index + 1) / total_stages * 100
        
        # Get next stage
        next_stage = None
        if current_stage_index >= 0 and current_stage_index < len(VIDEO_CREATION_FLOW) - 1:
            next_stage = VIDEO_CREATION_FLOW[current_stage_index + 1]
        
        status_data = {
            'video_id': video.id,
            'title': video.title,
            'status': video.status,
            'current_stage': video.stage,
            'next_stage': next_stage,
            'progress_percentage': round(progress_percentage, 1),
            'current_stage_index': current_stage_index + 1 if current_stage_index >= 0 else 0,
            'total_stages': len(VIDEO_CREATION_FLOW),
            'latest_execution_id': video.latest_execution_id,
            'error': video.error,
            'created_at': video.created_at,
            'estimated_completion': self._estimate_completion_time(video),
        }
        
        return Response(status_data)
    
    def _estimate_completion_time(self, video):
        """
        Estimate completion time based on current stage and historical data
        """
        # This is a simple estimation - you can improve it with historical data
        stage_estimates = {
            'script_generation': 2,      # 2 minutes
            'voice_generation': 5,     # 5 minutes
            'caption_generation': 1,   # 1 minute
            'image_prompt_generation': 2, # 2 minutes
            'image_generation': 10,    # 10 minutes
            'clip_creation': 5,        # 5 minutes
            'track_creation': 3,       # 3 minutes
            'video_composition': 15,   # 15 minutes
        }
        
        from .constants import VIDEO_CREATION_FLOW
        
        if video.stage not in VIDEO_CREATION_FLOW:
            return None
        
        current_index = VIDEO_CREATION_FLOW.index(video.stage)
        remaining_stages = VIDEO_CREATION_FLOW[current_index:]
        
        estimated_minutes = sum(stage_estimates.get(stage, 5) for stage in remaining_stages)
        
        from django.utils import timezone
        estimated_completion = timezone.now() + timezone.timedelta(minutes=estimated_minutes)
        
        return estimated_completion
    
    @action(detail=True, methods=['post'], url_path='retry/(?P<stage>[^/.]+)')
    def retry_stage(self, request, pk=None, stage=None):
        """
        Retry a specific stage of video creation process using Kafka events and video creation service
        
        URL: /api/videos/{video_id}/retry/{stage}
        Method: POST
        
        Args:
            stage: The stage to retry (script_generation, voice_generation, etc.)
        """
        video = self.get_object()
        
        # Get the user ID for tracking who requested the retry
        requested_by = str(request.user.id) if request.user.is_authenticated else 'anonymous'
        
        # Get optional reason from request body
        reason = request.data.get('reason', f'Manual retry requested via API by user {requested_by}')
        
        # Use video creation service for retry logic
        result = video_creation_service.retry_stage(
            video=video,
            stage=stage,
            requested_by=requested_by,
            reason=reason
        )
        
        # Return response based on service result
        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            # Determine HTTP status code based on error code
            error_code = result.get('error_code', 'UNKNOWN_ERROR')
            
            if error_code in ['INVALID_STAGE', 'INVALID_VIDEO_STATUS', 'INCOMPLETE_DEPENDENCIES']:
                http_status = status.HTTP_400_BAD_REQUEST
            elif error_code == 'MAX_RETRIES_EXCEEDED':
                http_status = status.HTTP_429_TOO_MANY_REQUESTS
            elif error_code == 'EVENT_PUBLISH_FAILED':
                http_status = status.HTTP_503_SERVICE_UNAVAILABLE
            else:
                http_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            
            return Response(result, status=http_status)
    
    @action(detail=True, methods=['post'])
    def proceed_to_next_stage(self, request, pk=None):
        """
        Manually proceed to the next stage of video creation process
        This endpoint is used when auto_approval_each_stage is False
        
        URL: /api/videos/{video_id}/proceed_to_next_stage
        Method: POST
        
        Body (optional):
        {
            "approve": true/false,
            "feedback": "Optional feedback for the current stage",
            "reason": "Reason for approval/rejection"
        }
        """
        video = self.get_object()
        
        # Get request data
        approve = request.data.get('approve', True)
        feedback = request.data.get('feedback', '')
        reason = request.data.get('reason', 'Manual stage progression via API')
        
        # Get the user ID for tracking
        requested_by = str(request.user.id) if request.user.is_authenticated else 'anonymous'
        
        # Validate that video allows manual progression
        if video.auto_approval_each_stage:
            return Response({
                'success': False,
                'error': 'This video has auto_approval_each_stage enabled. Manual progression is not required.',
                'auto_approval_enabled': True,
                'current_stage': video.stage
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate video is in a state that allows progression
        if video.status not in ['in_progress', 'stage_completed']:
            return Response({
                'success': False,
                'error': f'Cannot proceed from current video status: {video.status}',
                'current_status': video.status,
                'allowed_statuses': ['in_progress', 'stage_completed']
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Use video creation service for manual progression
        result = video_creation_service.proceed_to_next_stage(
            video=video,
            approve=approve,
            feedback=feedback,
            reason=reason,
            requested_by=requested_by
        )
        
        # Return response based on service result
        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            # Determine HTTP status code based on error code
            error_code = result.get('error_code', 'UNKNOWN_ERROR')
            
            if error_code in ['INVALID_VIDEO_STATUS', 'STAGE_NOT_READY', 'AUTO_APPROVAL_ENABLED']:
                http_status = status.HTTP_400_BAD_REQUEST
            elif error_code == 'STAGE_REJECTED':
                http_status = status.HTTP_422_UNPROCESSABLE_ENTITY
            else:
                http_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            
            return Response(result, status=http_status)


class TrackViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing tracks.
    """
    serializer_class = TrackSerializer
    
    def get_queryset(self):
        """
        Return tracks for the current authenticated user's videos.
        """
        return Track.objects.filter(video__user=self.request.user)
    
    def perform_create(self, serializer):
        """
        Check if the video belongs to the current user before creating a track.
        """
        video_id = self.request.data.get('video')
        video = get_object_or_404(Video, id=video_id, user=self.request.user)
        serializer.save(video=video)


class MediaGenerationViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing media generations.
    """
    serializer_class = MediaGenerationSerializer
    
    def get_queryset(self):
        """
        Return media generations for the current authenticated user's videos.
        """
        return MediaGeneration.objects.filter(video__user=self.request.user)
    
    def perform_create(self, serializer):
        """
        Check if the video belongs to the current user before creating a media generation.
        """
        video_id = self.request.data.get('video')
        video = get_object_or_404(Video, id=video_id, user=self.request.user)
        serializer.save(video=video)


class MediaAssetViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing media assets.
    """
    serializer_class = MediaAssetSerializer
    
    def get_queryset(self):
        """
        Return media assets for the current authenticated user's videos.
        """
        return MediaAsset.objects.filter(generation__video__user=self.request.user)


class ClipViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing clips.
    """
    serializer_class = ClipSerializer
    
    def get_queryset(self):
        """
        Return clips for the current authenticated user's videos.
        """
        return Clip.objects.filter(track__video__user=self.request.user)
    
    def perform_create(self, serializer):
        """
        Check if the track belongs to the current user's video before creating a clip.
        """
        track_id = self.request.data.get('track')
        track = get_object_or_404(Track, id=track_id, video__user=self.request.user)
        serializer.save(track=track)


class ConfigurationViewSet(viewsets.ViewSet):
    """
    API endpoint for configuration options (no database storage)
    """
    
    def list(self, request):
        """
        Get all configuration options including dynamic data from database
        """
        # Helper function to convert tuple of tuples to list of dicts
        def convert_choices_to_dict_list(choices):
            return [{'value': value, 'label': label} for value, label in choices]
        
        # Get unique TTS providers from database
        tts_providers_dict = {}
        for voice in TTSVoice.objects.filter(is_active=True):
            if voice.provider_name not in tts_providers_dict:
                tts_providers_dict[voice.provider_name] = {
                    'label': voice.provider_display_name,
                    'value': voice.provider_name
                }
        
        tts_providers = list(tts_providers_dict.values())
        
        # Get TTS voices from database
        tts_voices = TTSVoice.objects.filter(is_active=True)
        tts_voices_list = [
            {
                'id': voice.id,
                'name': voice.name,
                'provider': voice.provider_name,
                'display_name': voice.display_name,
                'language': voice.language,
                'gender': voice.gender
            } for voice in tts_voices
        ]
        
        # Get BGM options from database
        bgms = BGM.objects.filter(is_active=True)
        bgms_list = [
            {
                'id': bgm.id,
                'name': bgm.name,
                'display_name': bgm.display_name,
                'genre': bgm.genre,
                'duration': bgm.duration,
                'file_path': bgm.file_path
            } for bgm in bgms
        ]

        # Combine constants with dynamic data
        configuration_data = {
            'video_types': convert_choices_to_dict_list(VIDEO_TYPE_CHOICES),
            'script_types': convert_choices_to_dict_list(SCRIPT_TYPE_CHOICES),
            'speech_types': convert_choices_to_dict_list(SPEECH_TYPE_CHOICES),
            'video_styles': convert_choices_to_dict_list(VIDEO_STYLE_CHOICES),
            'orientations': convert_choices_to_dict_list(ORIENTATION_CHOICES),
            'durations': convert_choices_to_dict_list(DURATION_CHOICES),
            'video_statuses': convert_choices_to_dict_list(VIDEO_STATUS_CHOICES),
            'video_stages': convert_choices_to_dict_list(VIDEO_STAGE_CHOICES),
            'video_publish_statuses': convert_choices_to_dict_list(VIDEO_PUBLISH_STATUS_CHOICES),
            'task_status': convert_choices_to_dict_list(TASK_STATUS_CHOICES),
            'tts_providers': tts_providers,
            'tts_voices': tts_voices_list,
            'bgms': bgms_list,
        }
        
        return Response(configuration_data)


# Event-Driven Callback Views for N8N Integration
@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def receive_callback_response(request, stage):
    """
    Event-driven callback webhook to receive responses from N8N for each stage
    
    This endpoint immediately returns success to N8N and processes the callback asynchronously
    
    URL: /api/videos/callback/recieve-response/{stage}
    Method: POST
    
    Args:
        stage: The stage that completed (script, voice, caption, etc.)
    """
    try:
        # Immediately extract basic info and validate
        data = request.data
        input_data = data.get('input', {})
        output_data = data.get('output', {})
        video_id = input_data.get('video_id')
        response_status = data.get('status')  # 'success' or 'error'
        
        if not video_id:
            logger.error(f"Callback for stage '{stage}' missing video_id")
            # Still return 200 to N8N to avoid retries
            return Response({
                'status': 'received',
                'message': 'Callback received but video_id is missing',
                'error': 'video_id is required'
            }, status=status.HTTP_200_OK)
        
        # Basic video existence check
        try:
            video = Video.objects.get(id=video_id)
        except Video.DoesNotExist:
            logger.error(f"Video {video_id} not found for callback stage '{stage}'")
            # Still return 200 to N8N
            return Response({
                'status': 'received',
                'message': f'Callback received but video {video_id} not found',
                'error': f'Video {video_id} not found'
            }, status=status.HTTP_200_OK)
        
        # Get correlation ID from video
        correlation_id = getattr(video, 'correlation_id', str(__import__('uuid').uuid4()))
        
        logger.info(f"Received callback for video {video_id}, stage: {stage}, status: {response_status}")
        
        # Immediately return success to N8N
        response_data = {
            'status': 'received',
            'message': f'Callback for stage {stage} received successfully',
            'video_id': video.id,
            'stage': stage,
            'processing_status': 'queued'
        }
        
        # Publish callback received event asynchronously (fire and forget)
        try:
            from events.producer import event_publisher
            
            if response_status == 'success':
                # Publish successful callback event
                event_publisher.publish_n8n_callback_received(
                    video_id=video.id,
                    stage=stage,
                    correlation_id=correlation_id,
                    response_data=output_data,
                    execution_id=input_data.get('execution_id')
                )
                
                logger.info(f"Published N8NCallbackReceived event for video {video_id}, stage {stage}")
                
            elif response_status == 'error':
                # Publish stage failed event
                error_message = data.get('error_message', 'Unknown error occurred in N8N')
                event_publisher.publish_stage_failed(
                    video_id=video.id,
                    stage=stage,
                    correlation_id=correlation_id,
                    error_message=error_message
                )
                
                logger.warning(f"Published StageFailed event for video {video_id}, stage {stage}: {error_message}")
                
            else:
                # Invalid status - publish stage failed event
                error_message = f"Invalid callback status: {response_status}. Expected 'success' or 'error'"
                event_publisher.publish_stage_failed(
                    video_id=video.id,
                    stage=stage,
                    correlation_id=correlation_id,
                    error_message=error_message
                )
                
                logger.error(f"Invalid callback status for video {video_id}, stage {stage}: {response_status}")
                
        except Exception as e:
            # Even if event publishing fails, we still return success to N8N
            logger.error(f"Failed to publish callback event for video {video_id}, stage {stage}: {e}")
            response_data['warning'] = 'Event publishing failed but callback was received'
        
        # Always return success to N8N
        return Response(response_data, status=status.HTTP_200_OK)
    
    except Exception as e:
        # Log the error but still return success to N8N to avoid retries
        logger.error(f"Unexpected error processing callback for stage '{stage}': {str(e)}")
        
        return Response({
            'status': 'received',
            'message': f'Callback for stage {stage} received with processing error',
            'error': 'Internal processing error',
            'stage': stage
        }, status=status.HTTP_200_OK)
    