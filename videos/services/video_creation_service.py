"""
Enhanced video creation service with provider architecture
Supports both event-driven external providers and direct internal processing
"""
import logging
import uuid
from typing import Dict, Any, Optional
from django.conf import settings

from ..models import Video, Track, Clip, MediaAsset, MediaGeneration
from ..constants import (
    VIDEO_CREATION_FLOW, EXTERNAL_STAGES, INTERNAL_STAGES,
    STAGE_PROVIDERS, IMAGE_PROVIDER_MAPPING
)
from .providers import ProviderFactory, get_provider_for_stage, ProviderResponse, ProcessingStatus
from events.producer import event_publisher
from events.schemas import EventFactory

logger = logging.getLogger(__name__)


class VideoCreationService:
    """
    Enhanced video creation service with provider architecture
    Handles both event-driven external providers and direct internal processing
    """
    
    def __init__(self):
        # Initialize provider factory
        ProviderFactory.initialize()
    
    def start_video_creation(self, video: Video) -> bool:
        """
        Start the video creation process by publishing initial events
        
        Args:
            video: Video instance
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Generate correlation ID for tracking
            correlation_id = str(uuid.uuid4())
            
            # Set initial status
            video.status = 'in_progress'
            video.stage = 'script_generation'
            video.save()
            
            # Create workflow state
            from events.models import WorkflowState
            WorkflowState.objects.create(
                video_id=video,
                correlation_id=correlation_id,
                current_stage='script_generation'
            )
            
            # Publish video creation started event
            success = event_publisher.publish_video_creation_started(
                video_id=video.id,
                user_id=video.user_id,
                correlation_id=correlation_id,
                task_id=video.task_id if video.task else None,
                account_id=video.account_id if video.account else None,
                video_type=video.video_type
            )
            
            if not success:
                logger.error(f"Failed to publish VideoCreationStarted event for video {video.id}")
                return False
            
            # Publish stage initiation event for first stage
            success = event_publisher.publish_stage_initiated(
                video_id=video.id,
                stage='script_generation',
                correlation_id=correlation_id
            )
            
            if not success:
                logger.error(f"Failed to publish StageInitiated event for video {video.id}")
                return False
            
            logger.info(f"Started enhanced video creation for video {video.id}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting video creation for video {video.id}: {str(e)}")
            video.status = 'error'
            video.error = str(e)
            video.save()
            return False
    
    def handle_stage_initiation(self, video: Video, stage: str, correlation_id: str) -> bool:
        """
        Handle stage initiation using appropriate provider or internal processing
        
        Args:
            video: Video instance
            stage: The stage to initiate
            correlation_id: Correlation ID for tracking
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Handling stage initiation: {stage} for video {video.id}")
            
            # Validate stage
            if stage not in VIDEO_CREATION_FLOW:
                logger.error(f"Invalid stage: {stage}")
                return False
            
            # Route to appropriate handler
            if stage in INTERNAL_STAGES:
                return self._handle_internal_stage(video, stage, correlation_id)
            else:
                return self._handle_external_stage(video, stage, correlation_id)
                
        except Exception as e:
            logger.error(f"Error handling stage initiation for video {video.id}, stage {stage}: {str(e)}")
            return False
    
    def retry_stage(self, video: Video, stage: str, requested_by: str, reason: Optional[str] = None) -> Dict[str, Any]:
        """
        Retry a specific stage of video creation process with full validation and Kafka event publishing
        
        Args:
            video: Video instance
            stage: The stage to retry
            requested_by: User ID or identifier who requested the retry
            reason: Optional reason for retry
            
        Returns:
            Dict containing success status, messages, and relevant data
        """
        try:
            logger.info(f"Starting retry for video {video.id}, stage: {stage}, requested by: {requested_by}")
            
            # Validate stage
            if stage not in VIDEO_CREATION_FLOW:
                return {
                    'success': False,
                    'error': f'Invalid stage: {stage}',
                    'error_code': 'INVALID_STAGE',
                    'valid_stages': VIDEO_CREATION_FLOW
                }
            
            # Check if video is in a state that allows retry
            if video.status not in ['error', 'done', 'in_progress']:
                return {
                    'success': False,
                    'error': f'Cannot retry stage. Video status is "{video.status}". Retry is only allowed for videos with status "error", "done", or "in_progress".',
                    'error_code': 'INVALID_VIDEO_STATUS',
                    'current_status': video.status
                }
            
            # Check stage dependencies - ensure previous stages are completed
            stage_index = VIDEO_CREATION_FLOW.index(stage)
            previous_stages = VIDEO_CREATION_FLOW[:stage_index]
            
            # Get or create workflow state
            from events.models import WorkflowState
            try:
                workflow_state = WorkflowState.objects.get(video_id=video)
            except WorkflowState.DoesNotExist:
                # Create workflow state if it doesn't exist
                workflow_state = WorkflowState.objects.create(
                    video_id=video,
                    correlation_id=str(uuid.uuid4()),
                    current_stage=stage
                )
                logger.info(f"Created new workflow state for video {video.id}")
            
            # Validate that all previous stages are completed
            incomplete_stages = []
            for prev_stage in previous_stages:
                if not workflow_state.is_stage_completed(prev_stage):
                    incomplete_stages.append(prev_stage)
            
            if incomplete_stages:
                return {
                    'success': False,
                    'error': f'Cannot retry stage "{stage}". Previous stages must be completed first.',
                    'error_code': 'INCOMPLETE_DEPENDENCIES',
                    'incomplete_stages': incomplete_stages,
                    'completed_stages': workflow_state.stages_completed,
                    'suggestion': f'Please complete or retry the incomplete stages first: {", ".join(incomplete_stages)}'
                }
            
            # Reset retry count for the stage (fresh start on manual retry)
            logger.info(f"Resetting retry count for stage {stage} of video {video.id}")
            if stage in workflow_state.retry_counts:
                old_retry_count = workflow_state.retry_counts[stage]
                workflow_state.retry_counts[stage] = 0  # Reset to 0 so next failure starts at 1
                workflow_state.save(update_fields=['retry_counts', 'updated_at'])
                logger.info(f"Reset retry count from {old_retry_count} to 0 for stage {stage}")
            else:
                logger.info(f"No previous retry count found for stage {stage}")
            
            # Generate new correlation ID for retry
            retry_correlation_id = str(uuid.uuid4())
            
            # Publish retry requested event first
            retry_event_success = event_publisher.publish_retry_stage_requested(
                video_id=video.id,
                stage=stage,
                correlation_id=retry_correlation_id,
                requested_by=requested_by,
                reason=reason,
                metadata={
                    'original_correlation_id': workflow_state.correlation_id,
                    'video_status': video.status,
                    'workflow_stage': workflow_state.current_stage
                }
            )
            
            if not retry_event_success:
                logger.error(f"Failed to publish retry event for video {video.id}, stage {stage}")
                return {
                    'success': False,
                    'error': 'Failed to publish retry event to Kafka',
                    'error_code': 'EVENT_PUBLISH_FAILED'
                }
            
            # Update video state for retry
            video.status = 'in_progress'
            video.stage = stage
            video.error = None  # Clear previous error
            video.save()
            
            # Update workflow state
            workflow_state.current_stage = stage
            workflow_state.correlation_id = retry_correlation_id
            workflow_state.is_paused = False
            # Remove stage from failed list if it was there
            if stage in workflow_state.stages_failed:
                workflow_state.stages_failed.remove(stage)
            workflow_state.save()

            stage_success = event_publisher.publish_stage_initiated(
                video_id=video.id,
                stage=stage,
                correlation_id=retry_correlation_id
            )
            
            if stage_success:
                logger.info(f"Successfully initiated retry for video {video.id}, stage {stage}")
                
                return {
                    'success': True,
                    'message': f'Stage "{stage}" retry initiated successfully',
                    'data': {
                        'video_id': video.id,
                        'stage': stage,
                        'correlation_id': retry_correlation_id,
                        'video_status': video.status,
                        'workflow_state': {
                            'current_stage': workflow_state.current_stage,
                            'completed_stages': workflow_state.stages_completed,
                            'failed_stages': workflow_state.stages_failed,
                            'retry_counts': workflow_state.retry_counts
                        }
                    }
                }
            else:
                # Retry initiation failed
                video.status = 'error'
                video.error = f'Failed to initiate retry for stage "{stage}"'
                video.save()
                
                logger.error(f"Failed to initiate retry for video {video.id}, stage {stage}")
                
                return {
                    'success': False,
                    'error': f'Failed to initiate retry for stage "{stage}"',
                    'error_code': 'RETRY_INITIATION_FAILED',
                    'data': {
                        'video_id': video.id,
                        'stage': stage,
                    }
                }
                
        except Exception as e:
            logger.error(f"Error during retry initiation for video {video.id}, stage {stage}: {str(e)}")
            
            # Update video with error
            video.status = 'error'
            video.error = f'Retry initiation error: {str(e)}'
            video.save()
            
            # Publish stage failed event
            try:
                event_publisher.publish_stage_failed(
                    video_id=video.id,
                    stage=stage,
                    correlation_id=getattr(video, 'correlation_id', str(uuid.uuid4())),
                    error_message=f'Retry initiation exception: {str(e)}',
                    error_code='RETRY_EXCEPTION'
                )
            except Exception as event_error:
                logger.error(f"Failed to publish stage failed event: {event_error}")
            
            return {
                'success': False,
                'error': f'Retry initiation failed: {str(e)}',
                'error_code': 'RETRY_EXCEPTION',
                'data': {
                    'video_id': video.id,
                    'stage': stage
                }
            }
    
    def _handle_external_stage(self, video: Video, stage: str, correlation_id: str) -> bool:
        """
        Handle external stage processing using providers
        
        Args:
            video: Video instance
            stage: Stage name
            correlation_id: Correlation ID for tracking
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get appropriate provider for this stage
            provider = get_provider_for_stage(stage, video)
            
            # Log provider selection
            logger.info(f"Selected provider '{provider.provider_name}' for stage '{stage}', video {video.id}")
            
            # Process stage with provider
            response = provider.process_stage(video, stage, correlation_id)
            
            # Handle response based on type
            if response.status == ProcessingStatus.SUCCESS:
                # Stage completed successfully
                logger.info(f"Stage '{stage}' completed successfully for video {video.id}")
                
                # Publish stage completion event
                return event_publisher.publish_stage_completed(
                    video_id=video.id,
                    stage=stage,
                    correlation_id=correlation_id,
                    stage_data=response.data
                )
                
            elif response.status == ProcessingStatus.PENDING:
                # Async operation initiated
                logger.info(f"Stage '{stage}' initiated async processing for video {video.id}")
                
                # Update video with execution tracking
                if response.execution_id:
                    video.latest_execution_id = response.execution_id
                    video.save()
                
                # For async operations, we wait for callbacks
                return True
                
            else:
                # Stage failed
                logger.error(f"Stage '{stage}' failed for video {video.id}: {response.error_message}")
                
                # Publish stage failure event
                return event_publisher.publish_stage_failed(
                    video_id=video.id,
                    stage=stage,
                    correlation_id=correlation_id,
                    error_message=response.error_message,
                    error_code=response.error_code
                )
                
        except Exception as e:
            logger.error(f"Error in external stage processing for {stage}: {str(e)}")
            
            # Publish stage failure event
            return event_publisher.publish_stage_failed(
                video_id=video.id,
                stage=stage,
                correlation_id=correlation_id,
                error_message=str(e),
                error_code='EXTERNAL_STAGE_ERROR'
            )
    
    def _handle_internal_stage(self, video: Video, stage: str, correlation_id: str) -> bool:
        """
        Handle internal stage processing directly
        
        Args:
            video: Video instance
            stage: Stage name
            correlation_id: Correlation ID for tracking
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Processing internal stage '{stage}' for video {video.id}")
            
            if stage == 'track_creation':
                result = self._create_tracks(video, correlation_id)
            elif stage == 'clip_creation':
                result = self._create_clips(video, correlation_id)
            else:
                logger.error(f"Unknown internal stage: {stage}")
                return False
            
            if result:
                # Update video stage
                video.stage = stage
                video.save()
                
                # Publish stage completion event
                return event_publisher.publish_stage_completed(
                    video_id=video.id,
                    stage=stage,
                    correlation_id=correlation_id,
                    stage_data={'processed_internally': True}
                )
            else:
                # Publish stage failure event
                return event_publisher.publish_stage_failed(
                    video_id=video.id,
                    stage=stage,
                    correlation_id=correlation_id,
                    error_message=f"Internal processing failed for stage {stage}",
                    error_code='INTERNAL_STAGE_ERROR'
                )
                
        except Exception as e:
            logger.error(f"Error in internal stage processing for {stage}: {str(e)}")
            
            # Publish stage failure event
            return event_publisher.publish_stage_failed(
                video_id=video.id,
                stage=stage,
                correlation_id=correlation_id,
                error_message=str(e),
                error_code='INTERNAL_STAGE_ERROR'
            )
    
    def _create_tracks(self, video: Video, correlation_id: str) -> bool:
        """
        Create track records for the video
        Creates video track and audio tracks (speech + bgm if selected)
        
        Args:
            video: Video instance
            correlation_id: Correlation ID for tracking
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Creating tracks for video {video.id}")
            
            # Create video track (layer 0)
            video_track = Track.objects.create(
                video=video,
                type='video',
                layer=0
            )
            logger.info(f"Created video track {video_track.id}")
            
            # Create speech audio track (layer 1)
            speech_track = Track.objects.create(
                video=video,
                type='audio',
                layer=1
            )
            logger.info(f"Created speech audio track {speech_track.id}")
            
            # Create BGM audio track if BGM is selected (layer 2)
            if video.bgm:
                bgm_track = Track.objects.create(
                    video=video,
                    type='audio',
                    layer=2
                )
                logger.info(f"Created BGM audio track {bgm_track.id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to create tracks for video {video.id}: {str(e)}")
            return False
    
    def _create_clips(self, video: Video, correlation_id: str) -> bool:
        """
        Create clip records for each media asset and assign to appropriate tracks
        
        Args:
            video: Video instance
            correlation_id: Correlation ID for tracking
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Creating clips for video {video.id}")
            
            # Get tracks
            video_track = video.tracks.filter(type='video').first()
            speech_track = video.tracks.filter(type='audio', layer=1).first()
            bgm_track = video.tracks.filter(type='audio', layer=2).first()
            
            if not video_track or not speech_track:
                logger.error(f"Required tracks not found for video {video.id}")
                return False
            
            # Create clips for media assets (images/videos)
            media_assets = MediaAsset.objects.filter(
                generation__video=video,
                type__in=['image', 'video']
            ).order_by('created_at')
            
            current_time = 0.0
            clip_duration = video.duration / len(media_assets) if media_assets else video.duration
            
            for asset in media_assets:
                clip = Clip.objects.create(
                    track=video_track,
                    media=asset,
                    in_point=0.0,
                    out_point=clip_duration,
                    start_time=current_time,
                    opacity=1.0
                )
                logger.info(f"Created media clip {clip.id} for asset {asset.id}")
                current_time += clip_duration
            
            # Create speech clip if speech URL exists
            if video.speech_url:
                speech_clip = Clip.objects.create(
                    track=speech_track,
                    media=None,  # Direct URL, not MediaAsset
                    in_point=0.0,
                    out_point=video.duration,
                    start_time=0.0,
                    opacity=1.0,
                    volume=1.0
                )
                logger.info(f"Created speech clip {speech_clip.id}")
            
            # Create BGM clip if BGM is selected
            if video.bgm and bgm_track:
                bgm_clip = Clip.objects.create(
                    track=bgm_track,
                    media=None,  # BGM file path from video.bgm
                    in_point=0.0,
                    out_point=video.duration,
                    start_time=0.0,
                    opacity=1.0,
                    volume=0.3  # Lower volume for background music
                )
                logger.info(f"Created BGM clip {bgm_clip.id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to create clips for video {video.id}: {str(e)}")
            return False
    
    def proceed_to_next_stage(self, video: Video, approve: bool = True, feedback: str = '', 
                             reason: str = '', requested_by: str = 'system') -> Dict[str, Any]:
        """
        Manually proceed to the next stage of video creation process
        Used when auto_approval_each_stage is False
        
        Args:
            video: Video instance
            approve: Whether to approve and proceed to next stage
            feedback: Optional feedback for the current stage
            reason: Reason for approval/rejection
            requested_by: User ID who requested the progression
            
        Returns:
            Dict with success status, message, and additional data
        """
        try:
            from ..constants import VIDEO_CREATION_FLOW
            from events.producer import publish_stage_approved, publish_stage_rejected
            from events.schemas import create_stage_progression_requested
            
            # Validate video state
            if video.auto_approval_each_stage:
                return {
                    'success': False,
                    'error': 'Video has auto_approval_each_stage enabled. Manual progression not needed.',
                    'error_code': 'AUTO_APPROVAL_ENABLED',
                    'data': {
                        'video_id': video.id,
                        'auto_approval_enabled': True
                    }
                }
            
            if video.status not in ['in_progress', 'waiting_for_review']:
                return {
                    'success': False,
                    'error': f'Cannot proceed from current video status: {video.status}',
                    'error_code': 'INVALID_VIDEO_STATUS',
                    'data': {
                        'video_id': video.id,
                        'current_status': video.status,
                        'allowed_statuses': ['in_progress', 'waiting_for_review']
                    }
                }
            
            current_stage = video.stage
            if current_stage not in VIDEO_CREATION_FLOW:
                return {
                    'success': False,
                    'error': f'Invalid current stage: {current_stage}',
                    'error_code': 'INVALID_STAGE',
                    'data': {
                        'video_id': video.id,
                        'current_stage': current_stage
                    }
                }
            
            # Generate correlation ID for tracking
            correlation_id = f'manual-progression-{int(__import__("time").time())}-{video.id}'
            
            if not approve:
                # Stage rejected - publish rejection event and mark video as requiring attention
                video.status = 'stage_rejected'
                video.error = f'Stage {current_stage} rejected by user. Feedback: {feedback}'
                video.save()
                
                # Publish stage rejected event
                try:
                    publish_stage_rejected(
                        video_id=video.id,
                        stage=current_stage,
                        correlation_id=correlation_id,
                        feedback=feedback,
                        reason=reason,
                        rejected_by=requested_by
                    )
                except Exception as e:
                    logger.error(f"Failed to publish stage rejected event: {e}")
                
                return {
                    'success': True,
                    'message': f'Stage {current_stage} rejected successfully',
                    'action': 'stage_rejected',
                    'video_id': video.id,
                    'stage': current_stage,
                    'feedback': feedback,
                    'correlation_id': correlation_id
                }
            
            # Stage approved - proceed to next stage
            current_index = VIDEO_CREATION_FLOW.index(current_stage)
            
            if current_index >= len(VIDEO_CREATION_FLOW) - 1:
                # This is the last stage - mark video as completed
                video.status = 'completed'
                video.stage = 'completed'
                video.save()
                
                # Publish video completion event
                try:
                    from events.producer import publish_video_completed
                    publish_video_completed(
                        video_id=video.id,
                        correlation_id=correlation_id,
                        completion_data={
                            'manual_approval': True,
                            'feedback': feedback,
                            'approved_by': requested_by
                        }
                    )
                except Exception as e:
                    logger.error(f"Failed to publish video completed event: {e}")
                
                return {
                    'success': True,
                    'message': 'Video creation completed successfully',
                    'action': 'video_completed',
                    'video_id': video.id,
                    'final_stage': current_stage,
                    'correlation_id': correlation_id
                }
            
            # Move to next stage
            next_stage = VIDEO_CREATION_FLOW[current_index + 1]
            
            # Publish stage approved event
            try:
                publish_stage_approved(
                    video_id=video.id,
                    stage=current_stage,
                    next_stage=next_stage,
                    correlation_id=correlation_id,
                    feedback=feedback,
                    reason=reason,
                    approved_by=requested_by
                )
            except Exception as e:
                logger.error(f"Failed to publish stage approved event: {e}")
            
            # Update video to next stage
            video.stage = next_stage
            video.status = 'in_progress'
            video.error = None  # Clear any previous errors
            video.save()
            
            # Start processing the next stage
            success = self._handle_external_stage(video, next_stage, correlation_id)
            
            if success:
                return {
                    'success': True,
                    'message': f'Stage {current_stage} approved and proceeded to {next_stage}',
                    'action': 'stage_approved_and_proceeded',
                    'video_id': video.id,
                    'previous_stage': current_stage,
                    'current_stage': next_stage,
                    'feedback': feedback,
                    'correlation_id': correlation_id,
                    'next_stage_initiated': True
                }
            else:
                return {
                    'success': False,
                    'error': f'Stage {current_stage} approved but failed to initiate {next_stage}',
                    'error_code': 'NEXT_STAGE_INITIATION_FAILED',
                    'data': {
                        'video_id': video.id,
                        'previous_stage': current_stage,
                        'failed_stage': next_stage,
                        'correlation_id': correlation_id
                    }
                }
                
        except Exception as e:
            logger.error(f"Error during manual stage progression for video {video.id}: {str(e)}")
            
            # Update video with error
            video.status = 'error'
            video.error = f'Manual progression error: {str(e)}'
            video.save()
            
            return {
                'success': False,
                'error': f'Manual progression failed: {str(e)}',
                'error_code': 'PROGRESSION_EXCEPTION',
                'data': {
                    'video_id': video.id,
                    'stage': video.stage
                }
            }

# Global service instance
video_creation_service = VideoCreationService()
